{"rustc": 1842507548689473721, "features": "[\"alloc\", \"derive\", \"yoke\"]", "declared_features": "[\"alloc\", \"databake\", \"derive\", \"hashmap\", \"serde\", \"std\", \"yoke\"]", "target": 1825474209729987087, "profile": 2241668132362809309, "path": 8432994508129643697, "deps": [[9620753569207166497, "zerovec_derive", false, 5242928867843034229], [10706449961930108323, "yoke", false, 5827641575743068768], [17046516144589451410, "zerofrom", false, 3867144836092587581]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\zerovec-8be1bd6182278942\\dep-lib-zerovec", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}