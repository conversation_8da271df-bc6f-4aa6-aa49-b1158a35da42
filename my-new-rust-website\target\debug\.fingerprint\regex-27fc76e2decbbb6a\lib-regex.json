{"rustc": 1842507548689473721, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2241668132362809309, "path": 8951051242614557546, "deps": [[555019317135488525, "regex_automata", false, 9281130244260756105], [2779309023524819297, "aho_corasick", false, 2093084984410225624], [9408802513701742484, "regex_syntax", false, 4000226457587317961], [15932120279885307830, "memchr", false, 3749134897722373677]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-27fc76e2decbbb6a\\dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}