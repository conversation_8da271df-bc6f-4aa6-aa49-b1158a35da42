{"rustc": 1842507548689473721, "features": "[\"aws-lc-sys\", \"prebuilt-nasm\"]", "declared_features": "[\"alloc\", \"asan\", \"aws-lc-sys\", \"bindgen\", \"default\", \"fips\", \"non-fips\", \"prebuilt-nasm\", \"ring-io\", \"ring-sig-verify\", \"test_logging\", \"unstable\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 210498441986639191, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\aws-lc-rs-3cd3d9091f23aa33\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}