{"rustc": 1842507548689473721, "features": "[\"as_ref\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"std\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"std\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 7165309211519594838, "profile": 1218695365660037764, "path": 4131213780633027564, "deps": [[15774985133158646067, "derive_more_impl", false, 2454208502334106651]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_more-04a12a791877a228\\dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}