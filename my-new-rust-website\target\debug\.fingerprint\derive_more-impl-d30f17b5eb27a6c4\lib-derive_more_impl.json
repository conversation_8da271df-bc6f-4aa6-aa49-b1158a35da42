{"rustc": 1842507548689473721, "features": "[\"as_ref\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\"]", "declared_features": "[\"add\", \"add_assign\", \"as_ref\", \"constructor\", \"debug\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"full\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"mul\", \"mul_assign\", \"not\", \"sum\", \"testing-helpers\", \"try_from\", \"try_into\", \"try_unwrap\", \"unwrap\"]", "target": 11796376952621915773, "profile": 17818141490371658307, "path": 16175775318881716276, "deps": [[3060637413840920116, "proc_macro2", false, 12930012856289214077], [4974441333307933176, "syn", false, 8548530409345477052], [16126285161989458480, "unicode_xid", false, 13811130666070314101], [17990358020177143287, "quote", false, 6766174608057378244]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\derive_more-impl-d30f17b5eb27a6c4\\dep-lib-derive_more_impl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}