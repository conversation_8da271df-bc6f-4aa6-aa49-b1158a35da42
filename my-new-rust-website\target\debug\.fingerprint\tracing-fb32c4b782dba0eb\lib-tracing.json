{"rustc": 1842507548689473721, "features": "[\"attributes\", \"default\", \"log\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 11202463608144111571, "path": 17891693685379391602, "deps": [[325572602735163265, "tracing_attributes", false, 6649075517238266506], [1906322745568073236, "pin_project_lite", false, 2952522203648451890], [3424551429995674438, "tracing_core", false, 3745819432814515753], [5986029879202738730, "log", false, 14931304874147950142]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-fb32c4b782dba0eb\\dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}