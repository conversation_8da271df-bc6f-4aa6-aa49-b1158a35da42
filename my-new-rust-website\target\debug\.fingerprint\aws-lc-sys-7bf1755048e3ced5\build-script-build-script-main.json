{"rustc": 1842507548689473721, "features": "[\"prebuilt-nasm\"]", "declared_features": "[\"asan\", \"bindgen\", \"prebuilt-nasm\", \"ssl\"]", "target": 10419965325687163515, "profile": 2225463790103693989, "path": 4754361276137495615, "deps": [[7499741813737603141, "cmake", false, 16386611623173227152], [11989259058781683633, "dunce", false, 5450400642578495636], [13866570822711233627, "fs_extra", false, 11918298197825222382], [14929362752282690284, "cc", false, 4122404678273837978]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\aws-lc-sys-7bf1755048e3ced5\\dep-build-script-build-script-main", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}