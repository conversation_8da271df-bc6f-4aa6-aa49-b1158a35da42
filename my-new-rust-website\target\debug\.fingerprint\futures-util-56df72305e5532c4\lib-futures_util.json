{"rustc": 1842507548689473721, "features": "[\"alloc\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 1788798584831431502, "profile": 17467636112133979524, "path": 11817085148612214945, "deps": [[1615478164327904835, "pin_utils", false, 352784387199461576], [1906322745568073236, "pin_project_lite", false, 2952522203648451890], [7620660491849607393, "futures_core", false, 7091631627301195035], [16240732885093539806, "futures_task", false, 12584428867621392169]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-56df72305e5532c4\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}