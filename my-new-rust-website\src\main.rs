use actix_web::{get, App, HttpServer, Re<PERSON>ond<PERSON>};
use rustls::{pki_types::{<PERSON><PERSON>er, PrivateKeyDer}, ServerConfig};
use rustls_pemfile::{certs, pkcs8_private_keys};
use std::{fs::File, io::{BufReader, self}};

#[get("/hello")]
async fn hello() -> impl Responder {
    "Hello world!"
}

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    let config = load_rustls_config().map_err(|e| io::Error::new(io::ErrorKind::Other, format!("Failed to load rustls config: {}", e)))?;

    HttpServer::new(move || {
        App::new().service(hello)
    })
    .bind_rustls_0_23(("127.0.0.1", 8080), config)?
    .run()
    .await
}

fn load_rustls_config() -> Result<ServerConfig, Box<dyn std::error::Error>> {
    let config = ServerConfig::builder()
        .with_no_client_auth();

    let cert_file = &mut BufReader::new(File::open("certs/cert.pem")?);
    let key_file = &mut BufReader::new(File::open("certs/key.pem")?);

    let cert_chain = certs(cert_file)?.collect();

    let key_der = rustls_pemfile::private_key(key_file)?
        .ok_or_else(|| "No private key found in certs/key.pem".into())?;

    config.with_single_cert(cert_chain, key_der).map_err(Into::into)
}
